import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { AppState, MandalaChart, NavigationHistory } from '../types/mandala';
import { createMandalaChart, updateNodeContent, findNodeById } from '../utils/mandala';

// Action类型定义
type MandalaAction =
  | { type: 'CREATE_CHART'; payload: string }
  | { type: 'LOAD_CHART'; payload: MandalaChart }
  | { type: 'UPDATE_NODE_CONTENT'; payload: { nodeId: string; content: string } }
  | { type: 'NAVIGATE_TO_NODE'; payload: string }
  | { type: 'TOGGLE_EDIT_MODE' }
  | { type: 'GO_BACK' }
  | { type: 'GO_FORWARD' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// 初始状态
const initialState: AppState = {
  currentChart: null,
  isEditMode: false,
  navigationHistory: [],
  currentHistoryIndex: -1,
  isLoading: false,
  error: null,
};

// Reducer函数
const mandalaReducer = (state: AppState, action: MandalaAction): AppState => {
  switch (action.type) {
    case 'CREATE_CHART': {
      const newChart = createMandalaChart(action.payload);
      const history: NavigationHistory = {
        nodeId: newChart.rootNode.id,
        timestamp: new Date(),
      };
      
      return {
        ...state,
        currentChart: newChart,
        navigationHistory: [history],
        currentHistoryIndex: 0,
        error: null,
      };
    }
    
    case 'LOAD_CHART': {
      const history: NavigationHistory = {
        nodeId: action.payload.currentViewNodeId,
        timestamp: new Date(),
      };
      
      return {
        ...state,
        currentChart: action.payload,
        navigationHistory: [history],
        currentHistoryIndex: 0,
        error: null,
      };
    }
    
    case 'UPDATE_NODE_CONTENT': {
      if (!state.currentChart) return state;
      
      const updatedRootNode = updateNodeContent(
        state.currentChart.rootNode,
        action.payload.nodeId,
        action.payload.content
      );
      
      return {
        ...state,
        currentChart: {
          ...state.currentChart,
          rootNode: updatedRootNode,
          updatedAt: new Date(),
        },
      };
    }
    
    case 'NAVIGATE_TO_NODE': {
      if (!state.currentChart) return state;
      
      const targetNode = findNodeById(state.currentChart.rootNode, action.payload);
      if (!targetNode) return state;
      
      const newHistory: NavigationHistory = {
        nodeId: action.payload,
        timestamp: new Date(),
      };
      
      // 清除当前位置之后的历史记录，添加新记录
      const newNavigationHistory = [
        ...state.navigationHistory.slice(0, state.currentHistoryIndex + 1),
        newHistory,
      ];
      
      return {
        ...state,
        currentChart: {
          ...state.currentChart,
          currentViewNodeId: action.payload,
        },
        navigationHistory: newNavigationHistory,
        currentHistoryIndex: newNavigationHistory.length - 1,
      };
    }
    
    case 'TOGGLE_EDIT_MODE': {
      return {
        ...state,
        isEditMode: !state.isEditMode,
      };
    }
    
    case 'GO_BACK': {
      if (state.currentHistoryIndex <= 0 || !state.currentChart) return state;
      
      const newIndex = state.currentHistoryIndex - 1;
      const targetNodeId = state.navigationHistory[newIndex].nodeId;
      
      return {
        ...state,
        currentChart: {
          ...state.currentChart,
          currentViewNodeId: targetNodeId,
        },
        currentHistoryIndex: newIndex,
      };
    }
    
    case 'GO_FORWARD': {
      if (
        state.currentHistoryIndex >= state.navigationHistory.length - 1 ||
        !state.currentChart
      ) {
        return state;
      }
      
      const newIndex = state.currentHistoryIndex + 1;
      const targetNodeId = state.navigationHistory[newIndex].nodeId;
      
      return {
        ...state,
        currentChart: {
          ...state.currentChart,
          currentViewNodeId: targetNodeId,
        },
        currentHistoryIndex: newIndex,
      };
    }
    
    case 'SET_LOADING': {
      return {
        ...state,
        isLoading: action.payload,
      };
    }
    
    case 'SET_ERROR': {
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    }
    
    default:
      return state;
  }
};

// Context创建
const MandalaContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<MandalaAction>;
} | null>(null);

// Provider组件
export const MandalaProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(mandalaReducer, initialState);
  
  return (
    <MandalaContext.Provider value={{ state, dispatch }}>
      {children}
    </MandalaContext.Provider>
  );
};

// Hook
export const useMandala = () => {
  const context = useContext(MandalaContext);
  if (!context) {
    throw new Error('useMandala must be used within a MandalaProvider');
  }
  return context;
};
