import { AIModel, AIProviderType } from './types';

interface CacheEntry {
  models: AIModel[];
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface ModelCache {
  [key: string]: CacheEntry;
}

class ModelCacheService {
  private cache: ModelCache = {};
  private readonly DEFAULT_TTL = 30 * 60 * 1000; // 30分钟

  // 生成缓存键
  private getCacheKey(provider: AIProviderType, apiKey: string): string {
    // 使用provider和apiKey的前8位作为缓存键，避免泄露完整密钥
    const keyPrefix = apiKey.substring(0, 8);
    return `${provider}_${keyPrefix}`;
  }

  // 获取缓存的模型列表
  getCachedModels(provider: AIProviderType, apiKey: string): AIModel[] | null {
    const key = this.getCacheKey(provider, apiKey);
    const entry = this.cache[key];

    if (!entry) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      delete this.cache[key];
      return null;
    }

    return entry.models;
  }

  // 缓存模型列表
  cacheModels(
    provider: AIProviderType, 
    apiKey: string, 
    models: AIModel[], 
    ttl: number = this.DEFAULT_TTL
  ): void {
    const key = this.getCacheKey(provider, apiKey);
    this.cache[key] = {
      models: [...models], // 创建副本避免引用问题
      timestamp: Date.now(),
      ttl
    };
  }

  // 清除特定提供商的缓存
  clearCache(provider: AIProviderType, apiKey: string): void {
    const key = this.getCacheKey(provider, apiKey);
    delete this.cache[key];
  }

  // 清除所有缓存
  clearAllCache(): void {
    this.cache = {};
  }

  // 清理过期缓存
  cleanupExpiredCache(): void {
    const now = Date.now();
    Object.keys(this.cache).forEach(key => {
      const entry = this.cache[key];
      if (now - entry.timestamp > entry.ttl) {
        delete this.cache[key];
      }
    });
  }

  // 获取缓存统计信息
  getCacheStats(): { totalEntries: number; validEntries: number } {
    const now = Date.now();
    const totalEntries = Object.keys(this.cache).length;
    const validEntries = Object.values(this.cache).filter(
      entry => now - entry.timestamp <= entry.ttl
    ).length;

    return { totalEntries, validEntries };
  }

  // 检查是否有缓存
  hasCachedModels(provider: AIProviderType, apiKey: string): boolean {
    return this.getCachedModels(provider, apiKey) !== null;
  }
}

// 导出单例
export const modelCacheService = new ModelCacheService();

// 定期清理过期缓存（每10分钟）
setInterval(() => {
  modelCacheService.cleanupExpiredCache();
}, 10 * 60 * 1000);
