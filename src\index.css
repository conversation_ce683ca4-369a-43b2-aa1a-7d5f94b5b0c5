@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .mandala-grid {
    @apply grid grid-cols-3 gap-2 aspect-square w-full max-w-2xl mx-auto;
  }

  .mandala-cell {
    @apply relative border-2 border-gray-300 rounded-lg p-2 cursor-pointer transition-all duration-200 hover:border-blue-400 hover:shadow-md min-h-[80px] flex items-center justify-center;
  }

  .mandala-cell.center {
    @apply bg-blue-50 border-blue-400 font-semibold;
  }

  .mandala-cell.editable {
    @apply bg-white;
  }

  .mandala-cell.readonly {
    @apply bg-gray-50;
  }

  .ai-button {
    @apply absolute -top-2 -right-2 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 transition-opacity duration-200 hover:bg-purple-600 z-10;
  }

  .mandala-cell:hover .ai-button {
    @apply opacity-100;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .mandala-grid {
      @apply gap-1 max-w-sm;
    }

    .mandala-cell {
      @apply p-1 text-xs min-h-[60px];
    }

    .ai-button {
      @apply w-5 h-5 -top-1 -right-1;
    }
  }

  @media (max-width: 480px) {
    .mandala-grid {
      @apply gap-1 max-w-xs;
    }

    .mandala-cell {
      @apply p-1 text-xs min-h-[50px];
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1024px) {
    .mandala-grid {
      @apply gap-3 max-w-3xl;
    }

    .mandala-cell {
      @apply p-4 min-h-[100px];
    }
  }
}
