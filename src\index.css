@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-800;
    font-feature-settings: "rlig" 1, "calt" 1;
    min-height: 100vh;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
  }
}

@layer components {
  .mandala-grid {
    @apply grid grid-cols-3 gap-2 aspect-square w-full max-w-2xl mx-auto;
  }

  .mandala-cell {
    @apply relative border-2 border-gray-200 rounded-xl p-3 cursor-pointer transition-all duration-300 hover:border-blue-400 hover:shadow-lg hover:shadow-blue-100/50 min-h-[80px] flex items-center justify-center backdrop-blur-sm;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
    border-style: solid;
    transform: translateZ(0); /* 启用硬件加速 */
  }

  .mandala-cell:hover {
    transform: translateY(-2px) scale(1.02);
  }

  .mandala-cell.center {
    @apply bg-gradient-to-br from-blue-50 to-blue-100 border-blue-400 font-semibold shadow-md;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
  }

  .mandala-cell.editable {
    @apply bg-white shadow-sm;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%);
  }

  .mandala-cell.readonly {
    @apply bg-gray-50;
    background: linear-gradient(135deg, rgba(249,250,251,0.9) 0%, rgba(243,244,246,0.8) 100%);
  }

  .mandala-cell.border-dashed {
    border-style: dashed;
    @apply border-gray-300;
  }

  .ai-button {
    @apply absolute -top-2 -right-2 w-7 h-7 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 transition-all duration-300 hover:from-purple-600 hover:to-pink-600 z-10 shadow-lg;
    transform: scale(0.8);
  }

  .mandala-cell:hover .ai-button {
    @apply opacity-100;
    transform: scale(1);
  }

  .ai-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 20px rgba(168, 85, 247, 0.4);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .mandala-grid {
      @apply gap-1 max-w-sm;
    }

    .mandala-cell {
      @apply p-1 text-xs min-h-[60px];
    }

    .ai-button {
      @apply w-5 h-5 -top-1 -right-1;
    }
  }

  @media (max-width: 480px) {
    .mandala-grid {
      @apply gap-1 max-w-xs;
    }

    .mandala-cell {
      @apply p-1 text-xs min-h-[50px];
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1024px) {
    .mandala-grid {
      @apply gap-4 max-w-4xl;
    }

    .mandala-cell {
      @apply p-4 min-h-[120px];
    }
  }

  /* 加载动画 */
  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }

  /* 按钮样式增强 */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium px-4 py-2 rounded-lg border border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5;
  }

  .btn-ghost {
    @apply hover:bg-gray-100 text-gray-600 font-medium px-3 py-2 rounded-lg transition-all duration-200;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 backdrop-blur-sm;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  }

  /* 导航栏样式 */
  .navbar {
    @apply bg-white/80 backdrop-blur-md border-b border-gray-200/50 shadow-sm;
  }

  /* 对话框样式 */
  .dialog-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50;
    animation: fadeIn 0.2s ease-out;
  }

  .dialog-content {
    @apply bg-white rounded-xl shadow-2xl border border-gray-100 max-w-md w-full mx-4;
    animation: slideIn 0.3s ease-out;
  }

  /* 文本样式 */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
