import React, { useState, useEffect } from 'react';
import { 
  X, <PERSON>ting<PERSON>, Eye, EyeOff, TestTube, Check, AlertCircle, 
  RefreshCw, Loader2, Info, Zap, Clock, Database 
} from 'lucide-react';
import { storageService } from '../services/storageService';
import { aiServiceManager } from '../services/aiProviders/aiServiceManager';
import { AI_PROVIDERS, AIProviderType, AIModel } from '../services/aiProviders/types';

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SettingsDialog: React.FC<SettingsDialogProps> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState({
    apiProvider: AIProviderType.OPENAI,
    apiKey: '',
    apiBaseURL: '',
    model: '',
    temperature: 0.7,
    maxTokens: 1000,
    autoSave: true,
    autoSaveInterval: 30,
    customModels: ''
  });

  const [showApiKey, setShowApiKey] = useState(false);
  const [isTestingAPI, setIsTestingAPI] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string; latency?: number } | null>(null);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [dynamicModels, setDynamicModels] = useState<AIModel[]>([]);
  const [modelListResult, setModelListResult] = useState<string>('');

  // 加载设置
  useEffect(() => {
    if (isOpen) {
      const savedSettings = storageService.getSettings();
      setSettings(prev => ({
        ...prev,
        ...savedSettings,
        apiKey: savedSettings.aiApiKey || '',
      }));
      
      // 自动加载模型列表
      loadModelList();
    }
  }, [isOpen]);

  // 当提供商改变时重新加载模型列表
  useEffect(() => {
    if (isOpen && settings.apiKey) {
      loadModelList();
    }
  }, [settings.apiProvider, settings.apiKey, settings.apiBaseURL]);

  // 处理提供商变更
  const handleProviderChange = (providerId: AIProviderType) => {
    const provider = AI_PROVIDERS[providerId];
    if (provider) {
      setSettings(prev => ({
        ...prev,
        apiProvider: providerId,
        apiBaseURL: provider.baseURL,
        model: provider.models[0] || '',
      }));
      
      // 清空之前的模型列表和结果
      setDynamicModels([]);
      setModelListResult('');
    }
  };

  // 加载模型列表
  const loadModelList = async (forceRefresh: boolean = false) => {
    if (!settings.apiKey.trim()) {
      setDynamicModels([]);
      setModelListResult('');
      return;
    }

    setIsLoadingModels(true);
    setModelListResult('');

    try {
      // 临时设置AI服务配置
      aiServiceManager.setProvider(settings.apiProvider);
      aiServiceManager.updateConfig({
        apiKey: settings.apiKey,
        baseURL: settings.apiBaseURL,
        model: settings.model,
        temperature: settings.temperature,
        maxTokens: settings.maxTokens
      });

      const result = await aiServiceManager.fetchModelList(forceRefresh);
      
      if (result.success) {
        setDynamicModels(result.models);
        setModelListResult(result.message || `成功获取 ${result.models.length} 个模型`);
        
        // 如果当前选择的模型不在列表中，选择第一个可用模型
        if (result.models.length > 0 && !result.models.some(m => m.id === settings.model)) {
          setSettings(prev => ({ ...prev, model: result.models[0].id }));
        }
      } else {
        setDynamicModels([]);
        setModelListResult(result.message || '获取模型列表失败');
      }
    } catch (error) {
      setDynamicModels([]);
      setModelListResult(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoadingModels(false);
    }
  };

  // 刷新模型列表
  const refreshModelList = () => {
    loadModelList(true);
  };

  // 测试API连接
  const testAPIConnection = async () => {
    setIsTestingAPI(true);
    setTestResult(null);

    try {
      // 临时设置AI服务配置
      aiServiceManager.setProvider(settings.apiProvider);
      aiServiceManager.updateConfig({
        apiKey: settings.apiKey,
        baseURL: settings.apiBaseURL,
        model: settings.model,
        temperature: settings.temperature,
        maxTokens: settings.maxTokens
      });

      const result = await aiServiceManager.testConnection();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        message: `测试失败: ${error instanceof Error ? error.message : '未知错误'}`
      });
    } finally {
      setIsTestingAPI(false);
    }
  };

  // 保存设置
  const handleSave = () => {
    const settingsToSave = {
      ...settings,
      aiApiKey: settings.apiKey,
    };

    storageService.saveSettings(settingsToSave);
    
    // 更新AI服务配置
    aiServiceManager.setProvider(settings.apiProvider);
    aiServiceManager.updateConfig({
      apiKey: settings.apiKey,
      baseURL: settings.apiBaseURL,
      model: settings.model,
      temperature: settings.temperature,
      maxTokens: settings.maxTokens
    });

    onClose();
  };

  const currentProvider = AI_PROVIDERS[settings.apiProvider];
  const displayModels = dynamicModels.length > 0 
    ? dynamicModels 
    : (settings.apiProvider === AIProviderType.CUSTOM 
        ? settings.customModels.split(',').map(m => m.trim()).filter(Boolean).map(id => ({ id, name: id }))
        : currentProvider?.models.map(id => ({ id, name: id })) || []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Settings size={24} />
              </div>
              <div>
                <h2 className="text-2xl font-bold">AI 设置</h2>
                <p className="text-blue-100 text-sm">配置您的AI服务提供商和模型</p>
              </div>
            </div>
            <button 
              onClick={onClose} 
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Provider Selection */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-xl p-4 space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                  <Zap className="mr-2 text-blue-600" size={20} />
                  AI 提供商
                </h3>
                
                {/* Provider Cards */}
                <div className="space-y-2">
                  {Object.values(AI_PROVIDERS).map(provider => (
                    <div
                      key={provider.id}
                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                        settings.apiProvider === provider.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleProviderChange(provider.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-800">{provider.name}</h4>
                          <p className="text-xs text-gray-500">{provider.description}</p>
                        </div>
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          settings.apiProvider === provider.id
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {settings.apiProvider === provider.id && (
                            <Check size={12} className="text-white" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Configuration */}
            <div className="lg:col-span-2 space-y-6">
              {/* API Configuration */}
              <div className="bg-white border border-gray-200 rounded-xl p-6 space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                  <Database className="mr-2 text-green-600" size={20} />
                  API 配置
                </h3>

                {/* API Key */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API 密钥
                  </label>
                  <div className="relative">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      value={settings.apiKey}
                      onChange={(e) => setSettings(prev => ({ ...prev, apiKey: e.target.value }))}
                      placeholder="请输入API密钥..."
                      className="w-full px-4 py-3 pr-24 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                      <button
                        type="button"
                        onClick={() => setShowApiKey(!showApiKey)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 transition-colors"
                        title={showApiKey ? '隐藏密钥' : '显示密钥'}
                      >
                        {showApiKey ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                      <button
                        type="button"
                        onClick={testAPIConnection}
                        disabled={isTestingAPI || !settings.apiKey.trim()}
                        className="p-2 text-gray-400 hover:text-blue-600 disabled:opacity-50 rounded-md hover:bg-gray-100 transition-colors"
                        title="测试连接"
                      >
                        {isTestingAPI ? (
                          <Loader2 size={16} className="animate-spin" />
                        ) : (
                          <TestTube size={16} />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  {/* Test Result */}
                  {testResult && (
                    <div className={`mt-3 p-3 rounded-lg flex items-start space-x-2 ${
                      testResult.success 
                        ? 'bg-green-50 border border-green-200' 
                        : 'bg-red-50 border border-red-200'
                    }`}>
                      {testResult.success ? (
                        <Check size={16} className="text-green-600 mt-0.5" />
                      ) : (
                        <AlertCircle size={16} className="text-red-600 mt-0.5" />
                      )}
                      <div className="flex-1">
                        <p className={`text-sm font-medium ${
                          testResult.success ? 'text-green-800' : 'text-red-800'
                        }`}>
                          {testResult.message}
                        </p>
                        {testResult.latency && (
                          <p className="text-xs text-gray-600 mt-1 flex items-center">
                            <Clock size={12} className="mr-1" />
                            响应时间: {testResult.latency}ms
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* API Base URL */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API 基础URL
                  </label>
                  <input
                    type="text"
                    value={settings.apiBaseURL}
                    onChange={(e) => setSettings(prev => ({ ...prev, apiBaseURL: e.target.value }))}
                    placeholder="https://api.openai.com/v1"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {currentProvider?.description}
                  </p>
                </div>
              </div>

              {/* Model Selection */}
              <div className="bg-white border border-gray-200 rounded-xl p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                    <Database className="mr-2 text-purple-600" size={20} />
                    模型选择
                  </h3>
                  {aiServiceManager.supportsDynamicModels() && (
                    <button
                      onClick={refreshModelList}
                      disabled={isLoadingModels || !settings.apiKey.trim()}
                      className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 disabled:opacity-50 transition-colors"
                    >
                      <RefreshCw size={14} className={isLoadingModels ? 'animate-spin' : ''} />
                      <span>刷新模型</span>
                    </button>
                  )}
                </div>

                {/* Model List Status */}
                {modelListResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    dynamicModels.length > 0 
                      ? 'bg-green-50 text-green-700 border border-green-200'
                      : 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                  }`}>
                    <div className="flex items-center">
                      <Info size={14} className="mr-2" />
                      {modelListResult}
                    </div>
                  </div>
                )}

                {/* Custom Models Input */}
                {settings.apiProvider === AIProviderType.CUSTOM && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      自定义模型列表
                    </label>
                    <input
                      type="text"
                      value={settings.customModels}
                      onChange={(e) => setSettings(prev => ({ ...prev, customModels: e.target.value }))}
                      placeholder="输入模型名称，用逗号分隔"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      例如: gpt-3.5-turbo, gpt-4, custom-model-1
                    </p>
                  </div>
                )}

                {/* Model Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择模型
                  </label>
                  {displayModels.length > 0 ? (
                    <div className="space-y-2">
                      <select
                        value={settings.model}
                        onChange={(e) => setSettings(prev => ({ ...prev, model: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      >
                        {displayModels.map(model => (
                          <option key={model.id} value={model.id}>
                            {model.name || model.id}
                          </option>
                        ))}
                      </select>
                      
                      {/* Model Info */}
                      {dynamicModels.length > 0 && (
                        <div className="text-xs text-gray-600 bg-gray-50 p-3 rounded-lg">
                          {(() => {
                            const selectedModel = dynamicModels.find(m => m.id === settings.model);
                            if (selectedModel) {
                              return (
                                <div className="space-y-1">
                                  <p><strong>描述:</strong> {selectedModel.description}</p>
                                  {selectedModel.contextLength && (
                                    <p><strong>上下文长度:</strong> {selectedModel.contextLength.toLocaleString()} tokens</p>
                                  )}
                                </div>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Database size={32} className="mx-auto mb-2 opacity-50" />
                      <p>暂无可用模型</p>
                      <p className="text-xs">请检查API配置或点击刷新按钮</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              配置将自动保存到本地存储
            </div>
            <div className="flex space-x-3">
              <button 
                onClick={onClose} 
                className="px-6 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button 
                onClick={handleSave} 
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-md hover:shadow-lg"
              >
                保存设置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
