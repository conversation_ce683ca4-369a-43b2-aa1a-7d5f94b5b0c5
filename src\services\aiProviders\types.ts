// AI服务提供商的统一接口定义

export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface AIGenerationRequest {
  messages: AIMessage[];
  temperature?: number;
  maxTokens?: number;
  model?: string;
}

export interface AIGenerationResponse {
  content: string;
  usage?: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
}

export interface AITestConnectionResult {
  success: boolean;
  message: string;
  latency?: number;
}

export interface AIModel {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  pricing?: {
    input: number;
    output: number;
  };
}

export interface AIModelListResult {
  success: boolean;
  models: AIModel[];
  message?: string;
  cached?: boolean;
}

export interface AIProviderConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

// AI服务提供商抽象接口
export abstract class AIProvider {
  protected config: AIProviderConfig;

  constructor(config: AIProviderConfig) {
    this.config = config;
  }

  // 更新配置
  updateConfig(config: Partial<AIProviderConfig>) {
    this.config = { ...this.config, ...config };
  }

  // 测试连接
  abstract testConnection(): Promise<AITestConnectionResult>;

  // 生成单个回复
  abstract generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse>;

  // 获取支持的模型列表（静态）
  abstract getSupportedModels(): string[];

  // 获取提供商名称
  abstract getProviderName(): string;

  // 动态获取模型列表
  abstract fetchModelList(): Promise<AIModelListResult>;

  // 检查是否支持动态模型获取
  abstract supportsDynamicModels(): boolean;
}

// 支持的AI服务提供商类型
export enum AIProviderType {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic', 
  GEMINI = 'gemini',
  CUSTOM = 'custom'
}

// 提供商信息
export interface AIProviderInfo {
  id: AIProviderType;
  name: string;
  baseURL: string;
  models: string[];
  requiresKey: boolean;
  description?: string;
}

// 预定义的提供商信息
export const AI_PROVIDERS: Record<AIProviderType, AIProviderInfo> = {
  [AIProviderType.OPENAI]: {
    id: AIProviderType.OPENAI,
    name: 'OpenAI',
    baseURL: 'https://api.openai.com/v1',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-4o', 'gpt-3.5-turbo'],
    requiresKey: true,
    description: 'OpenAI GPT系列模型'
  },
  [AIProviderType.ANTHROPIC]: {
    id: AIProviderType.ANTHROPIC,
    name: 'Anthropic Claude',
    baseURL: 'https://api.anthropic.com/v1',
    models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-3-5-sonnet-20241022'],
    requiresKey: true,
    description: 'Anthropic Claude系列模型'
  },
  [AIProviderType.GEMINI]: {
    id: AIProviderType.GEMINI,
    name: 'Google Gemini',
    baseURL: 'https://generativelanguage.googleapis.com/v1beta',
    models: ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash'],
    requiresKey: true,
    description: 'Google Gemini系列模型'
  },
  [AIProviderType.CUSTOM]: {
    id: AIProviderType.CUSTOM,
    name: '自定义API',
    baseURL: '',
    models: [],
    requiresKey: true,
    description: '兼容OpenAI格式的自定义API'
  }
};
