import { AIGenerationRequest, AIGenerationResponse, MandalaNode } from '../types/mandala';
import { getLevelName } from '../utils/mandala';

// AI提示词模板
const AI_PROMPTS = {
  // 第2层：维度生成
  DIMENSION: `你是一个专业的思维导图专家。请基于给定的主题，生成8个不同的思考维度。

主题：{theme}

要求：
1. 生成8个不同的思考维度，每个维度用简洁的词语或短语表达（不超过10个字）
2. 维度要全面覆盖主题的各个方面
3. 维度之间要有逻辑关联但不重复
4. 适合进一步展开成具体的子主题
5. 按照重要性排序

请直接返回8个维度，每行一个，不需要编号：`,

  // 第3层：子主题生成
  SUBTOPIC: `你是一个专业的思维导图专家。请基于给定的维度，生成8个相关的子主题。

主题：{theme}
维度：{dimension}
上下文：{context}

要求：
1. 生成8个与该维度密切相关的子主题
2. 每个子主题用简洁明确的表达（不超过15个字）
3. 子主题要具体可操作，能够进一步分解为执行步骤
4. 覆盖该维度的不同方面
5. 按照重要性和逻辑顺序排列

请直接返回8个子主题，每行一个，不需要编号：`,

  // 第4层：执行步骤生成
  ACTION: `你是一个专业的项目管理专家。请基于给定的子主题，生成8个具体的执行步骤。

主题：{theme}
维度：{dimension}
子主题：{subtopic}
上下文：{context}

要求：
1. 生成8个具体可执行的行动步骤
2. 每个步骤要明确、具体、可衡量
3. 步骤之间要有逻辑顺序
4. 每个步骤用动词开头，表达清晰（不超过20个字）
5. 确保步骤的可操作性和实用性

请直接返回8个执行步骤，每行一个，不需要编号：`,

  // 单个内容生成
  SINGLE: `你是一个专业的思维导图专家。请基于给定的上下文，为指定位置生成一个合适的内容。

主题：{theme}
当前层级：{level}
位置：{position}
上下文：{context}

要求：
1. 生成一个与上下文相关的{level}内容
2. 内容要简洁明确，符合该层级的特点
3. 与其他内容形成良好的逻辑关联
4. 具有实用性和可操作性

请直接返回生成的内容，不需要额外说明：`,
};

// AI服务类
class AIService {
  private apiKey: string = '';
  private baseURL: string = 'https://api.openai.com/v1';
  private model: string = 'gpt-3.5-turbo';
  private temperature: number = 0.7;
  private maxTokens: number = 500;

  constructor() {
    // 从环境变量或配置中获取API密钥
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
  }

  // 设置API密钥
  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  // 设置基础URL
  setBaseURL(baseURL: string) {
    this.baseURL = baseURL;
  }

  // 设置模型
  setModel(model: string) {
    this.model = model;
  }

  // 设置温度
  setTemperature(temperature: number) {
    this.temperature = temperature;
  }

  // 设置最大令牌数
  setMaxTokens(maxTokens: number) {
    this.maxTokens = maxTokens;
  }

  // 获取当前配置（用于测试时备份）
  getCurrentConfig() {
    return {
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      model: this.model,
      temperature: this.temperature,
      maxTokens: this.maxTokens,
    };
  }

  // 测试API连接
  async testConnection(): Promise<{ success: boolean; message: string }> {
    if (!this.apiKey.trim()) {
      return { success: false, message: '请先输入API密钥' };
    }

    if (!this.baseURL.trim()) {
      return { success: false, message: '请先输入API基础URL' };
    }

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: '测试连接，请回复"连接成功"',
            },
          ],
          max_tokens: 10,
          temperature: 0,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // 如果不是JSON，使用状态码
        }

        return { success: false, message: `API请求失败: ${errorMessage}` };
      }

      const data = await response.json();

      if (data.choices && data.choices.length > 0) {
        return { success: true, message: 'API连接测试成功！' };
      } else {
        return { success: false, message: 'API返回格式异常' };
      }
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return { success: false, message: '网络连接失败，请检查URL是否正确' };
      }

      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
  
  // 生成单个内容
  async generateSingleContent(request: AIGenerationRequest): Promise<string> {
    const prompt = this.buildPrompt(request);

    // 如果没有API密钥，抛出错误而不是返回模拟数据
    if (!this.apiKey.trim()) {
      throw new Error('请先在设置中配置API密钥');
    }
    
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          max_tokens: this.maxTokens,
          temperature: this.temperature,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }
      
      const data = await response.json();
      return data.choices[0]?.message?.content?.trim() || '';
    } catch (error) {
      console.error('AI生成失败:', error);
      // 不再自动回退到模拟数据，而是抛出错误
      throw error;
    }
  }
  
  // 批量生成内容（生成8个子项）
  async generateBatchContent(request: AIGenerationRequest): Promise<string[]> {
    const prompt = this.buildBatchPrompt(request);

    // 如果没有API密钥，抛出错误
    if (!this.apiKey.trim()) {
      throw new Error('请先在设置中配置API密钥');
    }
    
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          max_tokens: this.maxTokens,
          temperature: this.temperature,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }
      
      const data = await response.json();
      const content = data.choices[0]?.message?.content?.trim() || '';
      
      // 解析返回的内容，按行分割
      const items = content.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .slice(0, 8); // 确保只取8个
      
      // 如果不足8个，用模拟数据补充
      while (items.length < 8) {
        items.push(`${getLevelName(request.level)}${items.length + 1}`);
      }
      
      return items;
    } catch (error) {
      console.error('AI批量生成失败:', error);
      // 不再自动回退到模拟数据，而是抛出错误
      throw error;
    }
  }
  
  // 构建单个内容生成的提示词
  private buildPrompt(request: AIGenerationRequest): string {
    const template = AI_PROMPTS.SINGLE;
    return template
      .replace('{theme}', request.context[0] || '未知主题')
      .replace('{level}', getLevelName(request.level))
      .replace('{position}', `第${request.position + 1}个位置`)
      .replace('{context}', request.context.join(' -> '));
  }
  
  // 构建批量内容生成的提示词
  private buildBatchPrompt(request: AIGenerationRequest): string {
    const context = request.context;
    const theme = context[0] || '未知主题';
    
    switch (request.level) {
      case 2: // 维度生成
        return AI_PROMPTS.DIMENSION.replace('{theme}', theme);
      
      case 3: // 子主题生成
        return AI_PROMPTS.SUBTOPIC
          .replace('{theme}', theme)
          .replace('{dimension}', context[1] || '未知维度')
          .replace('{context}', context.join(' -> '));
      
      case 4: // 执行步骤生成
        return AI_PROMPTS.ACTION
          .replace('{theme}', theme)
          .replace('{dimension}', context[1] || '未知维度')
          .replace('{subtopic}', context[2] || '未知子主题')
          .replace('{context}', context.join(' -> '));
      
      default:
        return this.buildPrompt(request);
    }
  }
  
  // 获取模拟单个内容
  private getMockContent(request: AIGenerationRequest): string {
    const levelName = getLevelName(request.level);
    const mockContents = {
      2: ['目标设定', '资源配置', '时间规划', '风险管理', '团队协作', '技能提升', '成果评估', '持续改进'],
      3: ['具体目标', '实施计划', '资源需求', '时间节点', '责任分工', '质量标准', '监控机制', '调整策略'],
      4: ['制定计划', '收集信息', '分析现状', '设定目标', '制定策略', '执行行动', '监控进度', '总结反思'],
    };
    
    const contents = mockContents[request.level as keyof typeof mockContents] || [`${levelName}内容`];
    return contents[request.position % contents.length];
  }
  
  // 获取模拟批量内容
  private getMockBatchContent(request: AIGenerationRequest): string[] {
    const levelName = getLevelName(request.level);
    const mockBatches = {
      2: ['目标设定', '资源配置', '时间规划', '风险管理', '团队协作', '技能提升', '成果评估', '持续改进'],
      3: ['具体目标', '实施计划', '资源需求', '时间节点', '责任分工', '质量标准', '监控机制', '调整策略'],
      4: ['制定详细计划', '收集相关信息', '分析当前现状', '设定明确目标', '制定执行策略', '开始执行行动', '监控执行进度', '总结经验教训'],
    };
    
    return mockBatches[request.level as keyof typeof mockBatches] || 
           Array.from({ length: 8 }, (_, i) => `${levelName}${i + 1}`);
  }
}

// 导出单例
export const aiService = new AIService();
