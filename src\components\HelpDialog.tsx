import React from 'react';
import { X, Keyboard, Mouse, Sparkles } from 'lucide-react';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

interface HelpDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const HelpDialog: React.FC<HelpDialogProps> = ({ isOpen, onClose }) => {
  const { shortcuts } = useKeyboardShortcuts();
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">使用帮助</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="space-y-6">
          {/* 基本操作 */}
          <div>
            <h3 className="flex items-center text-lg font-semibold mb-3">
              <Mouse size={18} className="mr-2" />
              基本操作
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>点击格子</span>
                <span className="text-gray-600">编辑模式：编辑内容 / 浏览模式：进入下一层</span>
              </div>
              <div className="flex justify-between">
                <span>悬浮格子</span>
                <span className="text-gray-600">显示AI生成按钮</span>
              </div>
              <div className="flex justify-between">
                <span>点击AI按钮</span>
                <span className="text-gray-600">智能生成内容</span>
              </div>
            </div>
          </div>
          
          {/* 键盘快捷键 */}
          <div>
            <h3 className="flex items-center text-lg font-semibold mb-3">
              <Keyboard size={18} className="mr-2" />
              键盘快捷键
            </h3>
            <div className="space-y-2 text-sm">
              {shortcuts.map((shortcut, index) => (
                <div key={index} className="flex justify-between">
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">
                    {shortcut.key}
                  </kbd>
                  <span className="text-gray-600">{shortcut.description}</span>
                </div>
              ))}
            </div>
          </div>
          
          {/* AI功能 */}
          <div>
            <h3 className="flex items-center text-lg font-semibold mb-3">
              <Sparkles size={18} className="mr-2" />
              AI智能生成
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• 空格子：生成单个内容</p>
              <p>• 有内容的格子：生成整个九宫格</p>
              <p>• 最后一层：重新生成当前内容</p>
              <p>• 支持根据上下文智能生成相关内容</p>
            </div>
          </div>
          
          {/* 层级说明 */}
          <div>
            <h3 className="text-lg font-semibold mb-3">层级结构</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• <strong>第1层</strong>：主题（中心思想）</p>
              <p>• <strong>第2层</strong>：维度（8个思考方向）</p>
              <p>• <strong>第3层</strong>：子主题（具体内容）</p>
              <p>• <strong>第4层</strong>：执行步骤（可操作的行动）</p>
            </div>
          </div>
          
          {/* 使用技巧 */}
          <div>
            <h3 className="text-lg font-semibold mb-3">使用技巧</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• 先确定主题，再让AI生成维度</p>
              <p>• 可以手动编辑AI生成的内容</p>
              <p>• 使用面包屑导航快速跳转</p>
              <p>• 定期保存工作成果</p>
              <p>• 利用导入导出功能分享思维导图</p>
            </div>
          </div>
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <button
            onClick={onClose}
            className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
          >
            知道了
          </button>
        </div>
      </div>
    </div>
  );
};
