import {
  AIProvider,
  AIGenerationRequest,
  AIGenerationResponse,
  AITestConnectionResult,
  AIProviderConfig,
  AIModelListResult,
  AIModel
} from './types';

// 自定义Provider，兼容OpenAI格式
export class CustomProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return '自定义API';
  }

  getSupportedModels(): string[] {
    return []; // 自定义模型由用户配置
  }

  supportsDynamicModels(): boolean {
    return true; // 支持通过OpenAI兼容API获取模型列表
  }

  async fetchModelList(): Promise<AIModelListResult> {
    if (!this.config.apiKey.trim()) {
      return {
        success: false,
        models: [],
        message: '请先配置API密钥'
      };
    }

    try {
      const response = await fetch(`${this.config.baseURL}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });

      if (!response.ok) {
        return {
          success: false,
          models: [],
          message: `获取模型列表失败: HTTP ${response.status}`
        };
      }

      const data = await response.json();

      // 尝试解析OpenAI格式的响应
      let models: AIModel[] = [];

      if (data.data && Array.isArray(data.data)) {
        // OpenAI格式
        models = data.data.map((model: any) => ({
          id: model.id,
          name: model.id,
          description: '自定义模型',
          contextLength: 4096
        }));
      } else if (Array.isArray(data)) {
        // 简单数组格式
        models = data.map((model: any) => ({
          id: typeof model === 'string' ? model : model.id,
          name: typeof model === 'string' ? model : (model.name || model.id),
          description: '自定义模型',
          contextLength: 4096
        }));
      }

      return {
        success: true,
        models,
        message: `成功获取 ${models.length} 个模型`
      };
    } catch (error) {
      return {
        success: false,
        models: [],
        message: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();

    if (!this.config.apiKey.trim()) {
      return {
        success: false,
        message: '请先配置API密钥',
        latency: 0
      };
    }

    if (!this.config.baseURL.trim()) {
      return {
        success: false,
        message: '请先配置API基础URL',
        latency: 0
      };
    }

    try {
      // 首先尝试获取模型列表（更轻量的测试）
      const modelsResponse = await this.testModelsEndpoint();
      if (modelsResponse.success) {
        return {
          success: true,
          message: '自定义API连接测试成功！（通过模型列表端点）',
          latency: Date.now() - startTime
        };
      }

      // 如果模型列表失败，尝试聊天完成端点
      const chatResponse = await this.testChatEndpoint(startTime);
      return chatResponse;

    } catch (error) {
      const latency = Date.now() - startTime;

      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '网络连接失败，请检查URL是否正确',
          latency
        };
      }

      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
        latency
      };
    }
  }

  private async testModelsEndpoint(): Promise<{ success: boolean; message: string }> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      const response = await fetch(`${this.config.baseURL}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return { success: true, message: '模型列表端点可用' };
      } else {
        return { success: false, message: `模型列表端点返回 ${response.status}` };
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return { success: false, message: '模型列表端点请求超时' };
      }
      return { success: false, message: '模型列表端点不可用' };
    }
  }

  private async testChatEndpoint(startTime: number): Promise<AITestConnectionResult> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        signal: controller.signal,
        body: JSON.stringify({
          model: this.config.model || 'gpt-3.5-turbo',
          messages: [
            {
              role: 'user',
              content: 'Hi'
            }
          ],
          max_tokens: 5,
          temperature: 0
        })
      });

      clearTimeout(timeoutId);
      const latency = Date.now() - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorData.message || errorMessage;
        } catch {
          // 如果不是JSON，使用原始文本
          if (errorText.length > 0 && errorText.length < 200) {
            errorMessage = errorText;
          }
        }

        return {
          success: false,
          message: `聊天端点测试失败: ${errorMessage}`,
          latency
        };
      }

      const data = await response.json();

      // 检查响应格式
      if (data.choices && Array.isArray(data.choices) && data.choices.length > 0) {
        return {
          success: true,
          message: '自定义API连接测试成功！',
          latency
        };
      } else if (data.error) {
        return {
          success: false,
          message: `API错误: ${data.error.message || data.error}`,
          latency
        };
      } else {
        return {
          success: false,
          message: 'API返回格式异常：缺少choices字段',
          latency
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;

      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          message: '请求超时，请检查网络连接或API响应速度',
          latency
        };
      }

      return {
        success: false,
        message: `聊天端点测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
        latency
      };
    }
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置API密钥');
    }

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: request.model || this.config.model,
          messages: request.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          temperature: request.temperature ?? this.config.temperature,
          max_tokens: request.maxTokens ?? this.config.maxTokens
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // 如果不是JSON，使用状态码
        }

        throw new Error(`自定义API错误: ${errorMessage}`);
      }

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('API返回格式异常：缺少choices字段');
      }

      const choice = data.choices[0];

      return {
        content: choice.message?.content || '',
        usage: data.usage ? {
          inputTokens: data.usage.prompt_tokens || 0,
          outputTokens: data.usage.completion_tokens || 0,
          totalTokens: data.usage.total_tokens || 0
        } : undefined,
        finishReason: choice.finish_reason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`自定义API调用失败: ${String(error)}`);
    }
  }
}

// Gemini API请求格式
interface GeminiContent {
  role?: 'user' | 'model';
  parts: Array<{
    text: string;
  }>;
}

interface GeminiRequest {
  contents: GeminiContent[];
  generationConfig?: {
    temperature?: number;
    maxOutputTokens?: number;
    candidateCount?: number;
  };
  systemInstruction?: {
    parts: Array<{
      text: string;
    }>;
  };
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

interface GeminiErrorResponse {
  error: {
    code: number;
    message: string;
    status: string;
  };
}

interface GeminiModelInfo {
  name: string;
  displayName: string;
  description: string;
  inputTokenLimit: number;
  outputTokenLimit: number;
  supportedGenerationMethods: string[];
}

interface GeminiModelsResponse {
  models: GeminiModelInfo[];
}

export class GeminiProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return 'Google Gemini';
  }

  getSupportedModels(): string[] {
    return [
      'gemini-pro',
      'gemini-pro-vision',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-2.0-flash-exp'
    ];
  }

  supportsDynamicModels(): boolean {
    return true;
  }

  async fetchModelList(): Promise<AIModelListResult> {
    if (!this.config.apiKey.trim()) {
      return {
        success: false,
        models: [],
        message: '请先配置API密钥'
      };
    }

    try {
      const response = await fetch(`${this.config.baseURL}/models?key=${this.config.apiKey}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData: GeminiErrorResponse = await response.json();
        return {
          success: false,
          models: [],
          message: `获取模型列表失败: ${errorData.error?.message || response.statusText}`
        };
      }

      const data: GeminiModelsResponse = await response.json();

      // 过滤并转换模型数据
      const models: AIModel[] = data.models
        .filter(model =>
          model.name.includes('gemini') &&
          model.supportedGenerationMethods.includes('generateContent')
        )
        .map(model => ({
          id: this.extractModelId(model.name),
          name: model.displayName || this.extractModelId(model.name),
          description: model.description,
          contextLength: model.inputTokenLimit
        }))
        .sort((a, b) => a.name.localeCompare(b.name));

      return {
        success: true,
        models,
        message: `成功获取 ${models.length} 个模型`
      };
    } catch (error) {
      return {
        success: false,
        models: [],
        message: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  private extractModelId(fullName: string): string {
    // 从 "models/gemini-pro" 提取 "gemini-pro"
    return fullName.split('/').pop() || fullName;
  }

  private getApiUrl(model: string): string {
    return `${this.config.baseURL}/models/${model}:generateContent`;
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();
    
    try {
      const url = `${this.getApiUrl(this.config.model)}?key=${this.config.apiKey}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: '测试连接，请回复"连接成功"'
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0,
            maxOutputTokens: 10,
            candidateCount: 1
          }
        } as GeminiRequest)
      });

      const latency = Date.now() - startTime;

      if (!response.ok) {
        const errorData: GeminiErrorResponse = await response.json();
        return {
          success: false,
          message: `API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`,
          latency
        };
      }

      const data: GeminiResponse = await response.json();
      
      if (data.candidates && data.candidates.length > 0) {
        return {
          success: true,
          message: 'Google Gemini API连接测试成功！',
          latency
        };
      } else {
        return {
          success: false,
          message: 'API返回格式异常：缺少candidates字段',
          latency
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '网络连接失败，请检查URL是否正确',
          latency
        };
      }
      
      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
        latency
      };
    }
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置Google Gemini API密钥');
    }

    // 分离系统消息和用户/助手消息
    let systemInstruction: { parts: Array<{ text: string }> } | undefined;
    const contents: GeminiContent[] = [];
    
    for (const msg of request.messages) {
      if (msg.role === 'system') {
        systemInstruction = {
          parts: [{ text: msg.content }]
        };
      } else {
        contents.push({
          role: msg.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: msg.content }]
        });
      }
    }

    const geminiRequest: GeminiRequest = {
      contents,
      generationConfig: {
        temperature: request.temperature ?? this.config.temperature,
        maxOutputTokens: request.maxTokens ?? this.config.maxTokens,
        candidateCount: 1
      }
    };

    // 如果有系统指令，添加到请求中
    if (systemInstruction) {
      geminiRequest.systemInstruction = systemInstruction;
    }

    try {
      const model = request.model || this.config.model;
      const url = `${this.getApiUrl(model)}?key=${this.config.apiKey}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(geminiRequest)
      });

      if (!response.ok) {
        const errorData: GeminiErrorResponse = await response.json();
        throw new Error(`Gemini API错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Gemini API返回格式异常：缺少candidates字段');
      }

      const candidate = data.candidates[0];
      
      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        throw new Error('Gemini API返回格式异常：缺少content.parts字段');
      }

      // 提取文本内容
      const textContent = candidate.content.parts
        .map(part => part.text)
        .join('');
      
      return {
        content: textContent,
        usage: {
          inputTokens: data.usageMetadata.promptTokenCount,
          outputTokens: data.usageMetadata.candidatesTokenCount,
          totalTokens: data.usageMetadata.totalTokenCount
        },
        finishReason: candidate.finishReason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Gemini API调用失败: ${String(error)}`);
    }
  }
}
