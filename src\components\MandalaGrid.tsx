import React from 'react';
import { MandalaNode } from '../types/mandala';
import { MandalaCell } from './MandalaCell';
import { useMandala } from '../contexts/MandalaContext';
import { getCurrentViewGrid } from '../utils/mandala';

interface MandalaGridProps {
  onNavigate?: (nodeId: string) => void;
  onAIGenerate?: (nodeId: string) => void;
  className?: string;
}

export const MandalaGrid: React.FC<MandalaGridProps> = ({
  onNavigate,
  onAIGenerate,
  className = '',
}) => {
  const { state } = useMandala();
  
  if (!state.currentChart) {
    return (
      <div className="mandala-grid">
        <div className="col-span-3 flex items-center justify-center text-gray-500">
          请先创建一个曼陀罗图
        </div>
      </div>
    );
  }
  
  const gridNodes = getCurrentViewGrid(state.currentChart);
  
  if (gridNodes.length === 0) {
    return (
      <div className="mandala-grid">
        <div className="col-span-3 flex items-center justify-center text-gray-500">
          无法加载九宫格数据
        </div>
      </div>
    );
  }
  
  // 确保九宫格按正确顺序排列
  const sortedNodes = [...gridNodes].sort((a, b) => a.position - b.position);
  
  return (
    <div className={`mandala-grid animate-fade-in ${className}`}>
      {sortedNodes.map((node, index) => (
        <MandalaCell
          key={node.id}
          node={node}
          onNavigate={onNavigate}
          onAIGenerate={onAIGenerate}
          className={`animate-slide-in`}
          style={{ animationDelay: `${index * 50}ms` }}
        />
      ))}
    </div>
  );
};
