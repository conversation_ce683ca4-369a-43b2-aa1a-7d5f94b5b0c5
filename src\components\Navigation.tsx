import React from 'react';
import {
  <PERSON>Left,
  ArrowRight,
  Home,
  Edit3,
  Eye,
  Plus,
  Save,
  Download,
  Upload,
  HelpCircle
} from 'lucide-react';
import { useMandala } from '../contexts/MandalaContext';
import { findNodeById, getNodePath, getLevelName } from '../utils/mandala';

interface NavigationProps {
  onCreateNew?: () => void;
  onSave?: () => void;
  onLoad?: () => void;
  onExport?: () => void;
  onShowHelp?: () => void;
  lastSaveTimeText?: string;
}

export const Navigation: React.FC<NavigationProps> = ({
  onCreateNew,
  onSave,
  onLoad,
  onExport,
  onShowHelp,
  lastSaveTimeText,
}) => {
  const { state, dispatch } = useMandala();
  
  const canGoBack = state.currentHistoryIndex > 0;
  const canGoForward = state.currentHistoryIndex < state.navigationHistory.length - 1;
  const canGoHome = state.currentChart && 
    state.currentChart.currentViewNodeId !== state.currentChart.rootNode.id;
  
  // 获取当前节点路径
  const getCurrentPath = () => {
    if (!state.currentChart) return [];
    
    const currentNode = findNodeById(
      state.currentChart.rootNode, 
      state.currentChart.currentViewNodeId
    );
    
    if (!currentNode) return [];
    
    return getNodePath(state.currentChart.rootNode, currentNode.id);
  };
  
  const currentPath = getCurrentPath();
  
  // 处理导航操作
  const handleGoBack = () => {
    if (canGoBack) {
      dispatch({ type: 'GO_BACK' });
    }
  };
  
  const handleGoForward = () => {
    if (canGoForward) {
      dispatch({ type: 'GO_FORWARD' });
    }
  };
  
  const handleGoHome = () => {
    if (state.currentChart && canGoHome) {
      dispatch({ 
        type: 'NAVIGATE_TO_NODE', 
        payload: state.currentChart.rootNode.id 
      });
    }
  };
  
  const handleToggleEditMode = () => {
    dispatch({ type: 'TOGGLE_EDIT_MODE' });
  };
  
  return (
    <div className="navbar px-2 sm:px-4 py-2 sm:py-3">
      <div className="flex items-center justify-between">
        {/* 左侧：导航控制 */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          {/* 后退按钮 */}
          <button
            onClick={handleGoBack}
            disabled={!canGoBack}
            className="p-1.5 sm:p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="后退"
          >
            <ArrowLeft size={14} className="sm:w-4 sm:h-4" />
          </button>
          
          {/* 前进按钮 */}
          <button
            onClick={handleGoForward}
            disabled={!canGoForward}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="前进"
          >
            <ArrowRight size={16} />
          </button>
          
          {/* 回到根节点 */}
          <button
            onClick={handleGoHome}
            disabled={!canGoHome}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="回到主题"
          >
            <Home size={16} />
          </button>
          
          {/* 分隔线 */}
          <div className="w-px h-6 bg-gray-300" />
          
          {/* 编辑模式切换 */}
          <button
            onClick={handleToggleEditMode}
            className={`p-2 rounded-lg border ${
              state.isEditMode 
                ? 'border-blue-500 bg-blue-50 text-blue-600' 
                : 'border-gray-300 hover:bg-gray-50'
            }`}
            title={state.isEditMode ? '退出编辑模式' : '进入编辑模式'}
          >
            {state.isEditMode ? <Eye size={16} /> : <Edit3 size={16} />}
          </button>
        </div>
        
        {/* 中间：面包屑导航 */}
        <div className="flex-1 mx-2 sm:mx-4 min-w-0">
          {currentPath.length > 0 && (
            <div className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm text-gray-600 overflow-x-auto">
              {currentPath.map((node, index) => (
                <React.Fragment key={node.id}>
                  {index > 0 && <span className="text-gray-400 flex-shrink-0">/</span>}
                  <button
                    onClick={() => dispatch({ type: 'NAVIGATE_TO_NODE', payload: node.id })}
                    className="hover:text-blue-600 hover:underline max-w-16 sm:max-w-32 truncate flex-shrink-0"
                    title={`${getLevelName(node.level)}: ${node.content}`}
                  >
                    {node.content || `${getLevelName(node.level)}`}
                  </button>
                </React.Fragment>
              ))}
            </div>
          )}
        </div>
        
        {/* 右侧：操作按钮 */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          {/* 新建 */}
          <button
            onClick={onCreateNew}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            title="新建曼陀罗图"
          >
            <Plus size={16} />
          </button>
          
          {/* 保存 */}
          <button
            onClick={onSave}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            title="保存"
          >
            <Save size={16} />
          </button>
          
          {/* 导入 */}
          <button
            onClick={onLoad}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            title="导入"
          >
            <Upload size={16} />
          </button>
          
          {/* 导出 */}
          <button
            onClick={onExport}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            title="导出"
          >
            <Download size={16} />
          </button>

          {/* 分隔线 */}
          <div className="w-px h-6 bg-gray-300" />

          {/* 帮助 */}
          <button
            onClick={onShowHelp}
            className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            title="帮助"
          >
            <HelpCircle size={16} />
          </button>
        </div>
      </div>
      
      {/* 状态信息 */}
      <div className="flex items-center justify-between mt-2">
        {/* 加载状态 */}
        {state.isLoading && (
          <div className="text-sm text-blue-600">
            正在处理...
          </div>
        )}

        {/* 保存状态 */}
        {!state.isLoading && lastSaveTimeText && (
          <div className="text-xs text-gray-500">
            {lastSaveTimeText}
          </div>
        )}

        {/* 错误信息 */}
        {state.error && (
          <div className="text-sm text-red-600 bg-red-50 px-3 py-1 rounded">
            {state.error}
          </div>
        )}
      </div>
    </div>
  );
};
