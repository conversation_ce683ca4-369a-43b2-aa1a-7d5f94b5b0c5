import { useEffect } from 'react';
import { useMandala } from '../contexts/MandalaContext';

export const useKeyboardShortcuts = () => {
  const { state, dispatch } = useMandala();
  
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 如果正在输入，不处理快捷键
      const activeElement = document.activeElement;
      if (
        activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.contentEditable === 'true'
      ) {
        return;
      }
      
      // Ctrl/Cmd + 组合键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'e':
            event.preventDefault();
            dispatch({ type: 'TOGGLE_EDIT_MODE' });
            break;
          case 'h':
            event.preventDefault();
            if (state.currentChart) {
              dispatch({ 
                type: 'NAVIGATE_TO_NODE', 
                payload: state.currentChart.rootNode.id 
              });
            }
            break;
          case 'z':
            event.preventDefault();
            // TODO: 实现撤销功能
            break;
          case 'y':
            event.preventDefault();
            // TODO: 实现重做功能
            break;
        }
        return;
      }
      
      // Alt + 组合键
      if (event.altKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault();
            dispatch({ type: 'GO_BACK' });
            break;
          case 'ArrowRight':
            event.preventDefault();
            dispatch({ type: 'GO_FORWARD' });
            break;
        }
        return;
      }
      
      // 单独按键
      switch (event.key) {
        case 'Escape':
          // 退出编辑模式或关闭对话框
          if (state.isEditMode) {
            dispatch({ type: 'TOGGLE_EDIT_MODE' });
          }
          break;
        case 'F2':
          // 进入编辑模式
          event.preventDefault();
          if (!state.isEditMode) {
            dispatch({ type: 'TOGGLE_EDIT_MODE' });
          }
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [state, dispatch]);
  
  // 返回快捷键说明
  const shortcuts = [
    { key: 'Ctrl+E', description: '切换编辑模式' },
    { key: 'Ctrl+H', description: '回到主题' },
    { key: 'Alt+←', description: '后退' },
    { key: 'Alt+→', description: '前进' },
    { key: 'F2', description: '进入编辑模式' },
    { key: 'Esc', description: '退出编辑模式' },
  ];
  
  return { shortcuts };
};
