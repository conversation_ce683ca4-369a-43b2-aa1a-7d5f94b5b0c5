import React, { useState } from 'react';
import { MandalaGrid } from './MandalaGrid';
import { Navigation } from './Navigation';
import { HelpDialog } from './HelpDialog';
import { useMandala } from '../contexts/MandalaContext';
import { useAIGeneration } from '../hooks/useAIGeneration';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';
import { findNodeById } from '../utils/mandala';

export const MandalaApp: React.FC = () => {
  const { state, dispatch } = useMandala();
  const { smartGenerate, isGenerating } = useAIGeneration();
  useKeyboardShortcuts(); // 启用键盘快捷键
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const [newChartTitle, setNewChartTitle] = useState('');
  
  // 处理节点导航
  const handleNavigate = (nodeId: string) => {
    const targetNode = findNodeById(state.currentChart?.rootNode!, nodeId);
    if (targetNode && targetNode.content.trim() !== '') {
      dispatch({ type: 'NAVIGATE_TO_NODE', payload: nodeId });
    }
  };
  
  // 处理AI生成
  const handleAIGenerate = async (nodeId: string) => {
    if (isGenerating) return;

    try {
      const success = await smartGenerate(nodeId);
      if (!success) {
        dispatch({ type: 'SET_ERROR', payload: 'AI生成失败，请稍后重试' });
      }
    } catch (error) {
      console.error('AI生成错误:', error);
      dispatch({ type: 'SET_ERROR', payload: 'AI生成过程中发生错误' });
    }
  };
  
  // 创建新图表
  const handleCreateNew = () => {
    setShowCreateDialog(true);
  };
  
  const handleConfirmCreate = () => {
    if (newChartTitle.trim()) {
      dispatch({ type: 'CREATE_CHART', payload: newChartTitle.trim() });
      setNewChartTitle('');
      setShowCreateDialog(false);
    }
  };
  
  const handleCancelCreate = () => {
    setNewChartTitle('');
    setShowCreateDialog(false);
  };
  
  // 保存功能
  const handleSave = () => {
    if (state.currentChart) {
      const dataStr = JSON.stringify(state.currentChart, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${state.currentChart.title}.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };
  
  // 导入功能
  const handleLoad = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const chartData = JSON.parse(e.target?.result as string);
            dispatch({ type: 'LOAD_CHART', payload: chartData });
          } catch (error) {
            dispatch({ type: 'SET_ERROR', payload: '文件格式错误' });
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };
  
  // 导出功能
  const handleExport = () => {
    handleSave(); // 目前导出和保存功能相同
  };

  // 显示帮助
  const handleShowHelp = () => {
    setShowHelpDialog(true);
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 导航栏 */}
      <Navigation
        onCreateNew={handleCreateNew}
        onSave={handleSave}
        onLoad={handleLoad}
        onExport={handleExport}
        onShowHelp={handleShowHelp}
      />
      
      {/* 主内容区域 */}
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        {state.currentChart ? (
          <div className="max-w-5xl mx-auto">
            {/* 当前视图标题 */}
            <div className="text-center mb-4 sm:mb-6">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-2">
                {state.currentChart.title}
              </h1>
              <p className="text-sm sm:text-base text-gray-600 px-2">
                {state.isEditMode ? '编辑模式 - 点击格子编辑内容' : '浏览模式 - 点击格子进入下一层'}
              </p>
            </div>

            {/* 九宫格 */}
            <div className="bg-white rounded-lg shadow-lg p-3 sm:p-6">
              <MandalaGrid
                onNavigate={handleNavigate}
                onAIGenerate={handleAIGenerate}
              />
            </div>
          </div>
        ) : (
          /* 欢迎页面 */
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h1 className="text-3xl font-bold text-gray-800 mb-4">
                曼陀罗思维导图
              </h1>
              <p className="text-gray-600 mb-8">
                基于曼陀罗思维的九宫格思维导图工具，支持AI智能生成内容
              </p>
              <button
                onClick={handleCreateNew}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                创建新的曼陀罗图
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* 创建对话框 */}
      {showCreateDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h2 className="text-xl font-bold mb-4">创建新的曼陀罗图</h2>
            <input
              type="text"
              value={newChartTitle}
              onChange={(e) => setNewChartTitle(e.target.value)}
              placeholder="请输入主题..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleConfirmCreate();
                } else if (e.key === 'Escape') {
                  handleCancelCreate();
                }
              }}
            />
            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={handleCancelCreate}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                取消
              </button>
              <button
                onClick={handleConfirmCreate}
                disabled={!newChartTitle.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                创建
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 帮助对话框 */}
      <HelpDialog
        isOpen={showHelpDialog}
        onClose={() => setShowHelpDialog(false)}
      />
    </div>
  );
};
